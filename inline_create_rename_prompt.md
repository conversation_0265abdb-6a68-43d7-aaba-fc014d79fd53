# Inline Create & Rename Feature Requirements

## Overview
Implement minipad2-style instant creation and inline renaming behavior for notes and folders in the tree view. When user clicks "New Note" or "New Folder", immediately create the item in the current directory and enter inline editing mode for naming.

## Current State vs. Required Behavior

### Current Behavior (Problematic)
```
User clicks "New Note" → Modal dialog appears → User types name → Clicks OK → Note created
```

### Required Behavior (minipad2-style)
```
User clicks "New Note" → Note immediately appears as "Untitled" → Text is selected for editing → User types new name → Presses Enter or clicks away → Name is saved
```

## 1. Instant Creation with Inline Editing

### 1.1 Enhanced New Note Creation Flow
```typescript
// Enhanced note creation with proper error handling and state management
const createNewNote = async (parentFolderId?: string): Promise<string | null> => {
  // Validate parent folder exists if specified
  if (parentFolderId && !await validateParentExists(parentFolderId)) {
    throw new Error(`Parent folder ${parentFolderId} does not exist`);
  }

  // Generate unique name to prevent conflicts
  const baseName = "Untitled";
  const uniqueName = await generateUniqueName(baseName, parentFolderId);

  let noteId: string | null = null;
  let rollbackRequired = false;

  try {
    // 1. Create note with optimistic UI update
    const optimisticNote: NoteNode = {
      id: `temp-${Date.now()}`, // Temporary ID for immediate UI feedback
      title: uniqueName,
      content: "",
      parentId: parentFolderId || null,
      children: [],
      isFolder: false,
      isExpanded: false,
      isPinned: false,
      isTemporary: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // 2. Add to UI immediately for instant feedback
    addOptimisticNoteToTree(optimisticNote);

    // 3. Create in database
    noteId = await createNote({
      title: uniqueName,
      content: "",
      parentId: parentFolderId || currentFolderId,
      isTemporary: true
    });

    rollbackRequired = true;

    // 4. Update optimistic note with real ID
    await updateOptimisticNote(optimisticNote.id, noteId);

    // 5. Enter edit mode with proper focus management
    await enterInlineEditMode(noteId, {
      selectAll: true,
      scrollIntoView: true,
      preventBlur: true
    });

    return noteId;

  } catch (error) {
    // Rollback optimistic update
    if (optimisticNote) {
      removeOptimisticNote(optimisticNote.id);
    }

    // Cleanup database if note was created
    if (rollbackRequired && noteId) {
      try {
        await deleteNote(noteId);
      } catch (cleanupError) {
        console.error('Failed to cleanup note after error:', cleanupError);
      }
    }

    // Show user-friendly error
    showErrorNotification('Failed to create note. Please try again.');
    throw error;
  }
};
```

### 1.2 Enhanced New Folder Creation Flow
```typescript
const createNewFolder = async (parentFolderId?: string): Promise<string | null> => {
  // Validate parent folder exists and is actually a folder
  if (parentFolderId) {
    const parentNode = await getNodeById(parentFolderId);
    if (!parentNode || !parentNode.isFolder) {
      throw new Error(`Invalid parent folder: ${parentFolderId}`);
    }
  }

  // Generate unique folder name
  const baseName = "New Folder";
  const uniqueName = await generateUniqueName(baseName, parentFolderId);

  let folderId: string | null = null;
  let rollbackRequired = false;

  try {
    // 1. Create optimistic folder for immediate UI feedback
    const optimisticFolder: NoteNode = {
      id: `temp-folder-${Date.now()}`,
      title: uniqueName,
      content: "",
      parentId: parentFolderId || null,
      children: [],
      isFolder: true,
      isExpanded: true, // Auto-expand new folders
      isPinned: false,
      isTemporary: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // 2. Add to UI immediately
    addOptimisticFolderToTree(optimisticFolder);

    // 3. Create in database
    folderId = await createFolder({
      name: uniqueName,
      parentId: parentFolderId || currentFolderId,
      isTemporary: true
    });

    rollbackRequired = true;

    // 4. Update optimistic folder with real ID
    await updateOptimisticFolder(optimisticFolder.id, folderId);

    // 5. Enter edit mode
    await enterInlineEditMode(folderId, {
      selectAll: true,
      scrollIntoView: true,
      preventBlur: true
    });

    return folderId;

  } catch (error) {
    // Rollback optimistic update
    if (optimisticFolder) {
      removeOptimisticFolder(optimisticFolder.id);
    }

    // Cleanup database if folder was created
    if (rollbackRequired && folderId) {
      try {
        await deleteFolder(folderId);
      } catch (cleanupError) {
        console.error('Failed to cleanup folder after error:', cleanupError);
      }
    }

    showErrorNotification('Failed to create folder. Please try again.');
    throw error;
  }
};
```

## 2. Inline Editing Implementation

### 2.1 Enhanced TreeNode Component Structure
```tsx
interface TreeNodeProps {
  node: NoteNode;
  level: number;
  isEditing: boolean;
  isLoading?: boolean;
  error?: string | null;
  onStartEdit: () => void;
  onFinishEdit: (newName: string) => Promise<void>;
  onCancelEdit: () => void;
  onNodeSelect: (node: NoteNode) => void;
  onToggleFolder: (folderId: string) => void;
}

interface EditState {
  value: string;
  isValidating: boolean;
  validationError: string | null;
  hasUnsavedChanges: boolean;
}

const TreeNode: React.FC<TreeNodeProps> = ({
  node,
  level,
  isEditing,
  isLoading = false,
  error = null,
  onStartEdit,
  onFinishEdit,
  onCancelEdit,
  onNodeSelect,
  onToggleFolder
}) => {
  const [editState, setEditState] = useState<EditState>({
    value: node.title || node.name || '',
    isValidating: false,
    validationError: null,
    hasUnsavedChanges: false
  });

  const inputRef = useRef<HTMLInputElement>(null);
  const nodeRef = useRef<HTMLDivElement>(null);
  const blurTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const validationTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (blurTimeoutRef.current) clearTimeout(blurTimeoutRef.current);
      if (validationTimeoutRef.current) clearTimeout(validationTimeoutRef.current);
    };
  }, []);

  // Reset edit state when node changes or editing stops
  useEffect(() => {
    if (!isEditing) {
      setEditState({
        value: node.title || node.name || '',
        isValidating: false,
        validationError: null,
        hasUnsavedChanges: false
      });
    }
  }, [isEditing, node.title, node.name]);

  // Enhanced focus management with accessibility
  useEffect(() => {
    if (isEditing && inputRef.current) {
      // Use requestAnimationFrame to ensure DOM is ready
      requestAnimationFrame(() => {
        if (inputRef.current) {
          inputRef.current.focus();
          inputRef.current.select();

          // Scroll into view if needed
          inputRef.current.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest',
            inline: 'nearest'
          });
        }
      });
    }
  }, [isEditing]);

  // Debounced validation
  const validateName = useCallback(async (name: string) => {
    if (validationTimeoutRef.current) {
      clearTimeout(validationTimeoutRef.current);
    }

    validationTimeoutRef.current = setTimeout(async () => {
      setEditState(prev => ({ ...prev, isValidating: true }));

      try {
        const validation = await validateNodeName(name, node.parentId, node.id);
        setEditState(prev => ({
          ...prev,
          isValidating: false,
          validationError: validation.isValid ? null : validation.error || 'Invalid name'
        }));
      } catch (error) {
        setEditState(prev => ({
          ...prev,
          isValidating: false,
          validationError: 'Validation failed'
        }));
      }
    }, 300); // 300ms debounce
  }, [node.parentId, node.id]);

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setEditState(prev => ({
      ...prev,
      value: newValue,
      hasUnsavedChanges: newValue !== (node.title || node.name || ''),
      validationError: null // Clear previous validation errors
    }));

    // Trigger validation for non-empty values
    if (newValue.trim()) {
      validateName(newValue.trim());
    }
  }, [validateName, node.title, node.name]);

  const handleKeyDown = useCallback(async (e: React.KeyboardEvent) => {
    // Prevent event bubbling to parent elements
    e.stopPropagation();

    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();

      const trimmedValue = editState.value.trim();
      if (!trimmedValue) {
        onCancelEdit();
        return;
      }

      if (editState.validationError) {
        // Show validation error but don't close edit mode
        return;
      }

      try {
        await onFinishEdit(trimmedValue);
      } catch (error) {
        console.error('Failed to finish edit:', error);
        setEditState(prev => ({
          ...prev,
          validationError: 'Failed to save changes'
        }));
      }
    } else if (e.key === 'Escape') {
      e.preventDefault();
      onCancelEdit();
    } else if (e.key === 'Tab') {
      // Allow tab navigation but finish edit first
      e.preventDefault();
      const trimmedValue = editState.value.trim();
      if (trimmedValue && !editState.validationError) {
        try {
          await onFinishEdit(trimmedValue);
        } catch (error) {
          console.error('Failed to finish edit on tab:', error);
        }
      } else {
        onCancelEdit();
      }
    }
  }, [editState.value, editState.validationError, onFinishEdit, onCancelEdit]);

  const handleBlur = useCallback(() => {
    // Use timeout to allow for click events on buttons
    blurTimeoutRef.current = setTimeout(async () => {
      const trimmedValue = editState.value.trim();

      if (!trimmedValue) {
        onCancelEdit();
        return;
      }

      if (editState.validationError) {
        // Don't save invalid names
        onCancelEdit();
        return;
      }

      try {
        await onFinishEdit(trimmedValue);
      } catch (error) {
        console.error('Failed to finish edit on blur:', error);
        onCancelEdit();
      }
    }, 150); // Small delay to handle click events
  }, [editState.value, editState.validationError, onFinishEdit, onCancelEdit]);

  const handleFocus = useCallback(() => {
    // Cancel blur timeout if user refocuses
    if (blurTimeoutRef.current) {
      clearTimeout(blurTimeoutRef.current);
      blurTimeoutRef.current = null;
    }
  }, []);

  return (
    <div
      ref={nodeRef}
      className={`tree-node ${isEditing ? 'editing' : ''} ${node.isTemporary ? 'temporary' : ''}`}
      style={{ paddingLeft: `${level * 20}px` }}
      role="treeitem"
      aria-expanded={node.isFolder ? node.isExpanded : undefined}
      aria-level={level + 1}
      aria-selected={false} // This should be managed by parent
      tabIndex={isEditing ? -1 : 0}
    >
      <div className="node-content">
        {/* Folder toggle button */}
        {node.isFolder && (
          <button
            onClick={() => onToggleFolder(node.id)}
            className="folder-toggle"
            aria-label={node.isExpanded ? 'Collapse folder' : 'Expand folder'}
            tabIndex={isEditing ? -1 : 0}
          >
            {node.isExpanded ? '�' : '�'}
          </button>
        )}

        {/* Node icon */}
        {!node.isFolder && (
          <span className="node-icon" aria-hidden="true">
            📄
          </span>
        )}

        {/* Loading indicator */}
        {isLoading && (
          <span className="loading-indicator" aria-hidden="true">
            ⏳
          </span>
        )}

        {/* Edit input or label */}
        {isEditing ? (
          <div className="edit-container">
            <input
              ref={inputRef}
              type="text"
              value={editState.value}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              onBlur={handleBlur}
              onFocus={handleFocus}
              className={`inline-edit-input ${editState.validationError ? 'error' : ''}`}
              maxLength={255}
              autoComplete="off"
              spellCheck={false}
              aria-label={`Edit ${node.isFolder ? 'folder' : 'note'} name`}
              aria-invalid={!!editState.validationError}
              aria-describedby={editState.validationError ? `${node.id}-error` : undefined}
              disabled={editState.isValidating}
            />

            {/* Validation feedback */}
            {editState.isValidating && (
              <span className="validation-spinner" aria-hidden="true">
                ⏳
              </span>
            )}

            {editState.validationError && (
              <div
                id={`${node.id}-error`}
                className="validation-error"
                role="alert"
                aria-live="polite"
              >
                {editState.validationError}
              </div>
            )}

            {/* Edit controls */}
            <div className="edit-controls">
              <button
                onClick={() => {
                  const trimmedValue = editState.value.trim();
                  if (trimmedValue && !editState.validationError) {
                    onFinishEdit(trimmedValue);
                  }
                }}
                disabled={!editState.value.trim() || !!editState.validationError || editState.isValidating}
                className="edit-confirm"
                aria-label="Confirm changes"
                title="Save (Enter)"
              >
                ✓
              </button>
              <button
                onClick={onCancelEdit}
                className="edit-cancel"
                aria-label="Cancel changes"
                title="Cancel (Escape)"
              >
                ✕
              </button>
            </div>
          </div>
        ) : (
          <span
            className={`node-label ${node.isPinned ? 'pinned' : ''} ${error ? 'error' : ''}`}
            onDoubleClick={onStartEdit}
            onClick={() => onNodeSelect(node)}
            role="button"
            tabIndex={0}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                onNodeSelect(node);
              } else if (e.key === 'F2') {
                e.preventDefault();
                onStartEdit();
              }
            }}
            aria-label={`${node.isFolder ? 'Folder' : 'Note'}: ${node.title || node.name}`}
          >
            {node.title || node.name}
            {node.isPinned && (
              <span className="pin-indicator" aria-label="Pinned">
                📌
              </span>
            )}
            {editState.hasUnsavedChanges && (
              <span className="unsaved-indicator" aria-label="Unsaved changes">
                •
              </span>
            )}
          </span>
        )}

        {/* Error display */}
        {error && !isEditing && (
          <div className="node-error" role="alert">
            {error}
          </div>
        )}
      </div>

      {/* Children */}
      {node.isFolder && node.isExpanded && node.children.length > 0 && (
        <div className="node-children" role="group">
          {node.children.map((child) => (
            <TreeNode
              key={child.id}
              node={child}
              level={level + 1}
              isEditing={false} // Only one node can be editing at a time
              onStartEdit={() => {}} // Implement proper edit management
              onFinishEdit={onFinishEdit}
              onCancelEdit={onCancelEdit}
              onNodeSelect={onNodeSelect}
              onToggleFolder={onToggleFolder}
            />
          ))}
        </div>
      )}
    </div>
  );
};
```

### 2.2 Edit Mode Styling
```css
.inline-edit-input {
  border: 1px solid #007acc;
  border-radius: 2px;
  padding: 1px 4px;
  font-size: inherit;
  font-family: inherit;
  background: white;
  outline: none;
  width: 100%;
  min-width: 100px;
}

.inline-edit-input:focus {
  border-color: #0078d4;
  box-shadow: 0 0 0 1px rgba(0, 120, 212, 0.3);
}

.node-label:hover {
  background-color: rgba(0, 0, 0, 0.04);
  cursor: pointer;
}

.tree-node {
  user-select: none; /* Prevent text selection on tree nodes */
}

.inline-edit-input {
  user-select: text; /* Allow text selection in edit mode */
}
```

## 3. Context Integration

### 3.1 Current Directory Awareness
```typescript
const TreeContext = createContext({
  currentFolderId: null as string | null,
  editingNodeId: null as string | null,
  setEditingNode: (nodeId: string | null) => {},
});

const useTreeContext = () => {
  const context = useContext(TreeContext);
  if (!context) {
    throw new Error('useTreeContext must be used within TreeProvider');
  }
  return context;
};
```

### 3.2 New Item Placement Logic
```typescript
const getNewItemParent = (selectedNodeId?: string): string | null => {
  if (!selectedNodeId) return null;
  
  const selectedNode = findNodeById(selectedNodeId);
  if (!selectedNode) return null;
  
  // If selected node is a folder, create inside it
  if (selectedNode.isFolder) {
    return selectedNode.id;
  }
  
  // If selected node is a note, create in same parent
  return selectedNode.parentId;
};
```

## 4. Keyboard Shortcuts & UX

### 4.1 Keyboard Shortcuts (macOS)
```typescript
// For Electron on macOS
const registerMacOSShortcuts = (window: BrowserWindow) => {
  // Global shortcuts using Command key (⌘)
  globalShortcut.register('CommandOrControl+N', () => {
    window.webContents.send('create-new-note');
  });
  
  globalShortcut.register('CommandOrControl+Shift+N', () => {
    window.webContents.send('create-new-folder');
  });
  
  // Note: Enter and Backspace are handled in the renderer process
  // as they are context-specific to the tree view component
};

// In renderer process - macOS-specific behavior
const handleKeyDown = (e: React.KeyboardEvent) => {
  const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
  
  if (e.key === 'Enter' && !e.shiftKey && !e.altKey && !e.ctrlKey && !(isMac && e.metaKey)) {
    // Enter key starts rename on macOS (Finder behavior)
    e.preventDefault();
    startRenameSelected();
  } else if (e.key === 'Backspace' && !e.shiftKey && !e.altKey && !e.ctrlKey && !(isMac && e.metaKey)) {
    // Backspace deletes on macOS (Finder behavior)
    e.preventDefault();
    deleteSelected();
  } else if (e.key === ' ' && !e.shiftKey && !e.altKey && !e.ctrlKey && !(isMac && e.metaKey)) {
    // Space for Quick Look style preview
    e.preventDefault();
    previewSelected();
  }
  
  // Handle inline editing keys
  if (isInEditMode) {
    if (e.key === 'Enter') {
      e.preventDefault();
      finishEdit();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      cancelEdit();
    }
  }
};
```

### 4.2 Context Menu Integration
```tsx
const ContextMenu = ({ node, position }) => (
  <div className="context-menu" style={{ top: position.y, left: position.x }}>
    <MenuItem onClick={() => createNewNote(node.id)}>
      📄 New Note
    </MenuItem>
    <MenuItem onClick={() => createNewFolder(node.id)}>
      📁 New Folder
    </MenuItem>
    <MenuSeparator />
    <MenuItem onClick={() => startRename(node.id)}>
      ✏️ Rename
    </MenuItem>
    <MenuItem onClick={() => deleteNode(node.id)}>
      🗑️ Delete
    </MenuItem>
  </div>
);
```

## 5. Data Management

### 5.1 Temporary Item Cleanup
```typescript
const handleEditComplete = async (nodeId: string, newName: string) => {
  if (!newName.trim()) {
    // User provided empty name - delete the temporary item
    await deleteNote(nodeId);
    removeFromTree(nodeId);
    return;
  }
  
  // Update with final name and mark as permanent
  await updateNote(nodeId, {
    title: newName.trim(),
    isTemporary: false
  });
  
  // Update tree display
  updateTreeNode(nodeId, { title: newName.trim() });
  setEditingNode(null);
};

const handleEditCancel = async (nodeId: string) => {
  const node = findNodeById(nodeId);
  if (node?.isTemporary) {
    // Delete temporary item
    await deleteNote(nodeId);
    removeFromTree(nodeId);
  }
  setEditingNode(null);
};
```

### 5.2 Duplicate Name Handling
```typescript
const generateUniqueName = (baseName: string, parentId: string | null): string => {
  const siblings = getChildNodes(parentId);
  const existingNames = siblings.map(s => s.title || s.name);
  
  if (!existingNames.includes(baseName)) {
    return baseName;
  }
  
  let counter = 1;
  let newName: string;
  do {
    newName = `${baseName} (${counter})`;
    counter++;
  } while (existingNames.includes(newName));
  
  return newName;
};
```

## 6. Visual Feedback & States

### 6.1 Creation Animation
```css
@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.tree-node.newly-created {
  animation: slideInFromLeft 0.2s ease-out;
}
```

### 6.2 Edit State Indicators
```tsx
const NodeIcon = ({ node, isEditing }) => {
  if (isEditing) {
    return <span className="edit-indicator">✏️</span>;
  }
  
  if (node.isTemporary) {
    return <span className="temp-indicator">⏳</span>;
  }
  
  return node.isFolder ? '📁' : '📄';
};
```

## 7. Error Handling & Edge Cases

### 7.1 Enhanced Name Validation
```typescript
interface ValidationResult {
  isValid: boolean;
  error?: string;
  warnings?: string[];
}

const validateNodeName = async (
  name: string,
  parentId: string | null,
  excludeId?: string
): Promise<ValidationResult> => {
  const trimmedName = name.trim();

  // Basic validation
  if (!trimmedName) {
    return { isValid: false, error: 'Name cannot be empty' };
  }

  if (trimmedName.length > 255) {
    return { isValid: false, error: 'Name too long (max 255 characters)' };
  }

  if (trimmedName.length < 1) {
    return { isValid: false, error: 'Name too short' };
  }

  // Platform-specific invalid characters
  const platform = navigator.platform.toLowerCase();
  let invalidChars: RegExp;

  if (platform.includes('win')) {
    // Windows invalid characters
    invalidChars = /[<>:"/\\|?*\x00-\x1f]/;
  } else if (platform.includes('mac')) {
    // macOS invalid characters (more permissive)
    invalidChars = /[:\x00-\x1f]/;
  } else {
    // Unix/Linux invalid characters
    invalidChars = /[/\x00]/;
  }

  if (invalidChars.test(trimmedName)) {
    return {
      isValid: false,
      error: `Name contains invalid characters for ${platform.includes('win') ? 'Windows' : platform.includes('mac') ? 'macOS' : 'this system'}`
    };
  }

  // Reserved names check
  const reservedNames = [
    'CON', 'PRN', 'AUX', 'NUL',
    'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9',
    'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
  ];

  if (platform.includes('win') && reservedNames.includes(trimmedName.toUpperCase())) {
    return { isValid: false, error: 'Name is reserved by the system' };
  }

  // Check for duplicate names in the same parent
  try {
    const siblings = await getSiblingNodes(parentId);
    const duplicateExists = siblings.some(sibling =>
      sibling.id !== excludeId &&
      sibling.title.toLowerCase() === trimmedName.toLowerCase()
    );

    if (duplicateExists) {
      return { isValid: false, error: 'A file or folder with this name already exists' };
    }
  } catch (error) {
    console.warn('Could not check for duplicates:', error);
    // Continue validation even if duplicate check fails
  }

  // Warnings for potentially problematic names
  const warnings: string[] = [];

  if (trimmedName.startsWith('.')) {
    warnings.push('Names starting with a dot are hidden on Unix systems');
  }

  if (trimmedName.includes('  ')) {
    warnings.push('Multiple consecutive spaces may cause display issues');
  }

  if (trimmedName !== trimmedName.trim()) {
    warnings.push('Leading or trailing spaces will be removed');
  }

  if (/^\d+$/.test(trimmedName)) {
    warnings.push('Numeric-only names may be confusing');
  }

  return {
    isValid: true,
    warnings: warnings.length > 0 ? warnings : undefined
  };
};

// Enhanced unique name generation with better conflict resolution
const generateUniqueName = async (
  baseName: string,
  parentId: string | null,
  maxAttempts: number = 100
): Promise<string> => {
  try {
    const siblings = await getSiblingNodes(parentId);
    const existingNames = new Set(
      siblings.map(s => (s.title || s.name || '').toLowerCase())
    );

    // If base name is available, use it
    if (!existingNames.has(baseName.toLowerCase())) {
      return baseName;
    }

    // Try numbered variations
    for (let i = 1; i <= maxAttempts; i++) {
      const candidate = `${baseName} (${i})`;
      if (!existingNames.has(candidate.toLowerCase())) {
        return candidate;
      }
    }

    // Fallback to timestamp-based name
    const timestamp = Date.now();
    return `${baseName} (${timestamp})`;

  } catch (error) {
    console.error('Error generating unique name:', error);
    // Fallback to timestamp-based name
    const timestamp = Date.now();
    return `${baseName} (${timestamp})`;
  }
};

// Helper function to get sibling nodes
const getSiblingNodes = async (parentId: string | null): Promise<NoteNode[]> => {
  try {
    if (parentId) {
      const parentNode = await getNodeById(parentId);
      return parentNode?.children || [];
    } else {
      // Get root level nodes
      const tree = await getNotesTree();
      return tree;
    }
  } catch (error) {
    console.error('Error getting sibling nodes:', error);
    return [];
  }
};
```

### 7.2 Enhanced State Management and Error Handling
```typescript
interface EditSession {
  nodeId: string;
  originalName: string;
  startTime: number;
  isTemporary: boolean;
}

interface OptimisticUpdate {
  id: string;
  type: 'create' | 'update' | 'delete';
  data: any;
  timestamp: number;
  rollback: () => Promise<void>;
}

// Enhanced edit lock with session management
const useEditLock = () => {
  const [editingSessions, setEditingSessions] = useState<Map<string, EditSession>>(new Map());
  const [optimisticUpdates, setOptimisticUpdates] = useState<Map<string, OptimisticUpdate>>(new Map());

  const startEdit = useCallback((nodeId: string, originalName: string, isTemporary: boolean = false): boolean => {
    setEditingSessions(prev => {
      if (prev.has(nodeId)) {
        console.warn(`Node ${nodeId} is already being edited`);
        return prev;
      }

      const newSessions = new Map(prev);
      newSessions.set(nodeId, {
        nodeId,
        originalName,
        startTime: Date.now(),
        isTemporary
      });
      return newSessions;
    });

    return true;
  }, []);

  const endEdit = useCallback((nodeId: string) => {
    setEditingSessions(prev => {
      const newSessions = new Map(prev);
      newSessions.delete(nodeId);
      return newSessions;
    });
  }, []);

  const getEditSession = useCallback((nodeId: string): EditSession | undefined => {
    return editingSessions.get(nodeId);
  }, [editingSessions]);

  const isEditing = useCallback((nodeId: string): boolean => {
    return editingSessions.has(nodeId);
  }, [editingSessions]);

  // Cleanup stale edit sessions (older than 5 minutes)
  const cleanupStaleSessions = useCallback(() => {
    const now = Date.now();
    const staleThreshold = 5 * 60 * 1000; // 5 minutes

    setEditingSessions(prev => {
      const newSessions = new Map(prev);
      for (const [nodeId, session] of prev) {
        if (now - session.startTime > staleThreshold) {
          console.warn(`Cleaning up stale edit session for node ${nodeId}`);
          newSessions.delete(nodeId);
        }
      }
      return newSessions;
    });
  }, []);

  // Auto-cleanup every minute
  useEffect(() => {
    const interval = setInterval(cleanupStaleSessions, 60000);
    return () => clearInterval(interval);
  }, [cleanupStaleSessions]);

  // Optimistic update management
  const addOptimisticUpdate = useCallback((update: OptimisticUpdate) => {
    setOptimisticUpdates(prev => new Map(prev).set(update.id, update));
  }, []);

  const removeOptimisticUpdate = useCallback((id: string) => {
    setOptimisticUpdates(prev => {
      const newUpdates = new Map(prev);
      newUpdates.delete(id);
      return newUpdates;
    });
  }, []);

  const rollbackOptimisticUpdate = useCallback(async (id: string) => {
    const update = optimisticUpdates.get(id);
    if (update) {
      try {
        await update.rollback();
        removeOptimisticUpdate(id);
      } catch (error) {
        console.error(`Failed to rollback optimistic update ${id}:`, error);
      }
    }
  }, [optimisticUpdates, removeOptimisticUpdate]);

  return {
    startEdit,
    endEdit,
    getEditSession,
    isEditing,
    cleanupStaleSessions,
    addOptimisticUpdate,
    removeOptimisticUpdate,
    rollbackOptimisticUpdate,
    editingSessions: Array.from(editingSessions.values()),
    optimisticUpdates: Array.from(optimisticUpdates.values())
  };
};

// Enhanced error handling with user-friendly messages
interface ErrorContext {
  operation: string;
  nodeId?: string;
  nodeName?: string;
  parentId?: string;
}

const handleTreeError = (error: Error, context: ErrorContext): string => {
  console.error(`Tree operation failed:`, error, context);

  // Network errors
  if (error.message.includes('fetch') || error.message.includes('network')) {
    return 'Network error. Please check your connection and try again.';
  }

  // Permission errors
  if (error.message.includes('permission') || error.message.includes('unauthorized')) {
    return 'Permission denied. You may not have access to perform this action.';
  }

  // Validation errors
  if (error.message.includes('validation') || error.message.includes('invalid')) {
    return error.message; // These are usually user-friendly already
  }

  // Database errors
  if (error.message.includes('database') || error.message.includes('sql')) {
    return 'Database error. Please try again or contact support.';
  }

  // Specific operation errors
  switch (context.operation) {
    case 'create_note':
      return 'Failed to create note. Please try again.';
    case 'create_folder':
      return 'Failed to create folder. Please try again.';
    case 'rename':
      return `Failed to rename "${context.nodeName}". Please try again.`;
    case 'delete':
      return `Failed to delete "${context.nodeName}". Please try again.`;
    default:
      return 'An unexpected error occurred. Please try again.';
  }
};

// Notification system for user feedback
interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  message: string;
  duration?: number;
  actions?: Array<{
    label: string;
    action: () => void;
  }>;
}

const useNotifications = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  const showNotification = useCallback((notification: Omit<Notification, 'id'>) => {
    const id = `notification-${Date.now()}-${Math.random()}`;
    const newNotification: Notification = {
      ...notification,
      id,
      duration: notification.duration ?? 5000
    };

    setNotifications(prev => [...prev, newNotification]);

    // Auto-remove after duration
    if (newNotification.duration > 0) {
      setTimeout(() => {
        removeNotification(id);
      }, newNotification.duration);
    }

    return id;
  }, []);

  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  }, []);

  const showErrorNotification = useCallback((message: string, actions?: Notification['actions']) => {
    return showNotification({
      type: 'error',
      message,
      actions,
      duration: 8000 // Longer duration for errors
    });
  }, [showNotification]);

  const showSuccessNotification = useCallback((message: string) => {
    return showNotification({
      type: 'success',
      message,
      duration: 3000
    });
  }, [showNotification]);

  return {
    notifications,
    showNotification,
    removeNotification,
    showErrorNotification,
    showSuccessNotification
  };
};
```

## 8. Implementation Checklist

### Phase 1: Basic Inline Creation
- [ ] Implement instant note/folder creation
- [ ] Add inline editing component
- [ ] Handle Enter/Escape/Blur events
- [ ] Add text selection on edit start

### Phase 2: Tree Integration  
- [ ] Integrate with existing tree view
- [ ] Add current directory context
- [ ] Implement parent folder detection
- [ ] Add visual feedback for new items

### Phase 3: Polish & UX
- [ ] Add keyboard shortcuts (Ctrl+N, F2, etc.)
- [ ] Implement context menu integration
- [ ] Add creation animations
- [ ] Handle name validation and duplicates

### Phase 4: Edge Cases
- [ ] Cleanup temporary items on app close
- [ ] Handle concurrent editing scenarios
- [ ] Add proper error handling
- [ ] Implement undo/redo for renames

## Expected User Experience

1. **Instant Gratification**: Click "New Note" → item appears immediately
2. **Intuitive Naming**: Text is pre-selected, ready for typing
3. **Forgiving**: Empty name cancels creation, no permanent empty items
4. **Consistent**: Same behavior for both notes and folders
5. **Keyboard Friendly**: All operations accessible via shortcuts
6. **Visual Clarity**: Clear indicators for edit state and temporary items

This implementation recreates the responsive, intuitive behavior that made minipad2 popular while modernizing it for contemporary web technologies.