/**
 * Enhanced Inline Create & Rename Styles
 * 
 * Comprehensive CSS for the enhanced inline editing functionality
 * with improved accessibility, animations, and visual feedback.
 */

/* ============================================================================
   TREE NODE BASE STYLES
   ============================================================================ */

.tree-node {
  position: relative;
  user-select: none;
  transition: all 0.2s ease;
  border-radius: 4px;
  margin: 1px 0;
}

.tree-node:focus-within {
  outline: 2px solid #007acc;
  outline-offset: 1px;
}

.tree-node.editing {
  background-color: #f8f9ff;
  border-left: 4px solid #007acc;
  box-shadow: 0 2px 8px rgba(0, 122, 204, 0.1);
  z-index: 10;
}

.tree-node.temporary {
  background-color: #fffbf0;
  border-left: 3px solid #ffa500;
  opacity: 0.9;
}

.tree-node.newly-created {
  background-color: #f0f8ff;
  border-left: 4px solid #4a90e2;
  animation: slideInFromLeft 0.3s ease-out, highlightFade 2s ease-out 0.3s;
}

/* ============================================================================
   NODE CONTENT LAYOUT
   ============================================================================ */

.node-content {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  min-height: 28px;
  gap: 6px;
}

.folder-toggle {
  background: none;
  border: none;
  padding: 2px;
  cursor: pointer;
  border-radius: 2px;
  transition: background-color 0.15s ease;
  font-size: 14px;
  line-height: 1;
}

.folder-toggle:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.folder-toggle:focus {
  outline: 2px solid #007acc;
  outline-offset: 1px;
}

.node-icon {
  font-size: 14px;
  line-height: 1;
  opacity: 0.8;
}

.loading-indicator {
  font-size: 12px;
  animation: spin 1s linear infinite;
}

/* ============================================================================
   INLINE EDITING STYLES
   ============================================================================ */

.edit-container {
  display: flex;
  align-items: center;
  flex: 1;
  gap: 4px;
  position: relative;
}

.inline-edit-input {
  flex: 1;
  border: 2px solid #007acc;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 13px;
  font-family: inherit;
  background: white;
  outline: none;
  min-width: 120px;
  transition: border-color 0.15s ease, box-shadow 0.15s ease;
}

.inline-edit-input:focus {
  border-color: #0078d4;
  box-shadow: 0 0 0 3px rgba(0, 120, 212, 0.2);
}

.inline-edit-input.error {
  border-color: #d13438;
  background-color: #fef7f7;
}

.inline-edit-input.error:focus {
  border-color: #d13438;
  box-shadow: 0 0 0 3px rgba(209, 52, 56, 0.2);
}

.inline-edit-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* ============================================================================
   VALIDATION AND FEEDBACK
   ============================================================================ */

.validation-spinner {
  font-size: 12px;
  animation: spin 1s linear infinite;
  color: #666;
}

.validation-error {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #d13438;
  color: white;
  padding: 4px 8px;
  border-radius: 0 0 4px 4px;
  font-size: 11px;
  z-index: 20;
  animation: slideDown 0.2s ease-out;
}

.validation-error::before {
  content: '';
  position: absolute;
  top: -4px;
  left: 8px;
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 4px solid #d13438;
}

/* ============================================================================
   EDIT CONTROLS
   ============================================================================ */

.edit-controls {
  display: flex;
  gap: 2px;
  margin-left: 4px;
}

.edit-confirm,
.edit-cancel {
  background: none;
  border: 1px solid transparent;
  padding: 2px 6px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 11px;
  line-height: 1;
  transition: all 0.15s ease;
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-confirm {
  color: #28a745;
  border-color: #28a745;
}

.edit-confirm:hover:not(:disabled) {
  background-color: #28a745;
  color: white;
}

.edit-confirm:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.edit-cancel {
  color: #dc3545;
  border-color: #dc3545;
}

.edit-cancel:hover {
  background-color: #dc3545;
  color: white;
}

.edit-confirm:focus,
.edit-cancel:focus {
  outline: 2px solid currentColor;
  outline-offset: 1px;
}

/* ============================================================================
   NODE LABELS AND INDICATORS
   ============================================================================ */

.node-label {
  flex: 1;
  font-size: 13px;
  line-height: 1.4;
  cursor: pointer;
  padding: 2px 4px;
  border-radius: 3px;
  transition: background-color 0.15s ease;
  display: flex;
  align-items: center;
  gap: 4px;
  min-height: 20px;
}

.node-label:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.node-label:focus {
  outline: 2px solid #007acc;
  outline-offset: 1px;
  background-color: rgba(0, 122, 204, 0.1);
}

.node-label.pinned {
  font-weight: 600;
}

.node-label.error {
  color: #d13438;
  background-color: #fef7f7;
}

.pin-indicator {
  font-size: 11px;
  opacity: 0.8;
}

.unsaved-indicator {
  color: #ffa500;
  font-weight: bold;
  font-size: 16px;
  line-height: 1;
}

/* ============================================================================
   ERROR DISPLAY
   ============================================================================ */

.node-error {
  background: #fef7f7;
  color: #d13438;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  margin-left: 4px;
  border: 1px solid #f5c6cb;
}

/* ============================================================================
   CHILDREN CONTAINER
   ============================================================================ */

.node-children {
  margin-left: 16px;
  border-left: 1px solid #e0e0e0;
  padding-left: 4px;
}

/* ============================================================================
   ANIMATIONS
   ============================================================================ */

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes highlightFade {
  from {
    background-color: #f0f8ff;
  }
  to {
    background-color: transparent;
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* ============================================================================
   ACCESSIBILITY IMPROVEMENTS
   ============================================================================ */

@media (prefers-reduced-motion: reduce) {
  .tree-node,
  .inline-edit-input,
  .edit-confirm,
  .edit-cancel,
  .node-label,
  .folder-toggle {
    transition: none;
  }
  
  .newly-created {
    animation: none;
  }
  
  .loading-indicator,
  .validation-spinner {
    animation: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .tree-node.editing {
    border-left-width: 6px;
  }
  
  .inline-edit-input {
    border-width: 3px;
  }
  
  .validation-error {
    font-weight: bold;
  }
}

/* ============================================================================
   RESPONSIVE DESIGN
   ============================================================================ */

@media (max-width: 768px) {
  .node-content {
    padding: 6px 8px;
    min-height: 32px;
  }
  
  .inline-edit-input {
    font-size: 14px;
    padding: 6px 8px;
  }
  
  .edit-controls {
    gap: 4px;
  }
  
  .edit-confirm,
  .edit-cancel {
    min-width: 24px;
    height: 24px;
    font-size: 12px;
  }
}
