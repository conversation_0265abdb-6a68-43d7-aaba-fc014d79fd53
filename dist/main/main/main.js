"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const path_1 = require("path");
const tray_1 = require("./tray");
const shortcuts_1 = require("./shortcuts");
const ipc_1 = require("./ipc");
const notes_1 = require("../database/notes");
const clipboard_1 = require("./clipboard");
const logger_1 = require("../utils/logger");
class NexusApp {
    constructor() {
        this.mainWindow = null;
        this.tray = null;
        this.notesDB = null;
        this.clipboardManager = null;
        this.setupApp();
    }
    setupApp() {
        console.log('setupApp called');
        // Ensure single instance
        const gotTheLock = electron_1.app.requestSingleInstanceLock();
        console.log('Got lock:', gotTheLock);
        if (!gotTheLock) {
            console.log('Another instance is running, quitting');
            electron_1.app.quit();
            return;
        }
        // Handle second instance attempt
        electron_1.app.on('second-instance', () => {
            // Someone tried to run a second instance, focus our window instead
            if (this.mainWindow) {
                if (this.mainWindow.isMinimized()) {
                    this.mainWindow.restore();
                }
                this.mainWindow.show();
                this.mainWindow.focus();
            }
        });
        // Handle app ready
        electron_1.app.whenReady().then(() => {
            console.log('App ready event fired');
            try {
                console.log('About to create window');
                this.createWindow();
                console.log('Window created successfully');
                this.setupTray();
                this.setupShortcuts();
                this.setupDatabase();
                this.setupClipboard();
                this.setupIPC();
                console.log('App initialization completed');
            }
            catch (error) {
                console.error('Error during app initialization:', error);
            }
        });
        // Handle window closed
        electron_1.app.on('window-all-closed', () => {
            // On macOS, keep app running even when all windows are closed
            if (process.platform !== 'darwin') {
                electron_1.app.quit();
            }
        });
        // Handle app activation (macOS)
        electron_1.app.on('activate', () => {
            if (electron_1.BrowserWindow.getAllWindows().length === 0) {
                this.createWindow();
            }
        });
        // Handle before quit
        electron_1.app.on('before-quit', () => {
            logger_1.SafeLogger.info('App is quitting...');
            this.cleanup();
        });
        // Handle process termination signals gracefully
        process.on('SIGINT', () => {
            logger_1.SafeLogger.info('Received SIGINT, shutting down gracefully...');
            electron_1.app.quit();
        });
        process.on('SIGTERM', () => {
            logger_1.SafeLogger.info('Received SIGTERM, shutting down gracefully...');
            electron_1.app.quit();
        });
        // Handle uncaught exceptions to prevent crashes
        process.on('uncaughtException', (error) => {
            logger_1.SafeLogger.error('Uncaught exception:', error);
            // Check if this is an EIO error and handle gracefully
            if (error.message && error.message.includes('EIO')) {
                logger_1.SafeLogger.error('EIO error detected - likely due to process termination or file system issues');
                this.cleanup();
                // Don't exit immediately for EIO errors, they're often recoverable
                return;
            }
            // For other critical errors, show dialog and exit gracefully
            if (this.mainWindow && !this.mainWindow.isDestroyed()) {
                this.mainWindow.webContents.executeJavaScript(`
          alert('A critical error occurred: ${error.message}. The application will restart.');
        `).catch(() => {
                    // Ignore if webContents is not ready
                }).finally(() => {
                    electron_1.app.relaunch();
                    electron_1.app.exit(1);
                });
            }
            else {
                electron_1.app.relaunch();
                electron_1.app.exit(1);
            }
        });
        process.on('unhandledRejection', (reason, promise) => {
            logger_1.SafeLogger.error('Unhandled rejection at:', promise, 'reason:', reason);
            // Check if this is an EIO-related rejection
            const reasonStr = String(reason);
            if (reasonStr.includes('EIO') || reasonStr.includes('write')) {
                logger_1.SafeLogger.error('EIO-related promise rejection detected');
                this.cleanup();
                return;
            }
            // Don't exit immediately for promise rejections, just log them
        });
    }
    createWindow() {
        // Prevent creating multiple windows
        if (this.mainWindow) {
            this.mainWindow.show();
            this.mainWindow.focus();
            return;
        }
        this.mainWindow = new electron_1.BrowserWindow({
            width: 1200,
            height: 800,
            minWidth: 600,
            minHeight: 400,
            titleBarStyle: 'hiddenInset',
            trafficLightPosition: { x: -100, y: -100 }, // Hide traffic lights
            backgroundColor: '#ffffff', // White background
            show: false, // Don't show until ready
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                preload: (0, path_1.join)(__dirname, '../preload/preload.js'),
                webSecurity: false, // Disable for localhost in development
            },
        });
        // Load the React app
        if (process.env.NODE_ENV === 'development') {
            logger_1.SafeLogger.info('Loading development URL: http://localhost:5173');
            this.mainWindow.loadURL('http://localhost:5173');
            // Auto-open DevTools for debugging
            this.mainWindow.webContents.openDevTools();
            // Force show window immediately for debugging
            this.mainWindow.show();
            this.mainWindow.focus();
            this.mainWindow.moveTop();
        }
        else {
            this.mainWindow.loadFile((0, path_1.join)(__dirname, '../../renderer/index.html'));
        }
        // Handle window events
        this.mainWindow.on('closed', () => {
            this.mainWindow = null;
        });
        // Hide window instead of closing on macOS
        this.mainWindow.on('close', (event) => {
            if (process.platform === 'darwin') {
                event.preventDefault();
                this.mainWindow?.hide();
            }
        });
        // Handle content loading
        this.mainWindow.webContents.on('did-finish-load', () => {
            logger_1.SafeLogger.info('Renderer content loaded');
            // Show and focus the window after content loads
            this.mainWindow?.show();
            this.mainWindow?.focus();
            this.mainWindow?.moveTop();
        });
        this.mainWindow.webContents.on('did-fail-load', (_event, errorCode, errorDescription, validatedURL) => {
            logger_1.SafeLogger.error('Failed to load renderer:', errorCode, errorDescription, validatedURL);
        });
        // Window is already shown, just log when it's ready
        this.mainWindow.once('ready-to-show', () => {
            logger_1.SafeLogger.info('Window ready to show');
        });
    }
    setupTray() {
        if (this.mainWindow) {
            this.tray = new tray_1.SimpleTray(this.mainWindow);
        }
    }
    setupShortcuts() {
        if (this.mainWindow) {
            (0, shortcuts_1.registerShortcuts)(this.mainWindow);
        }
    }
    setupDatabase() {
        try {
            const dbPath = (0, path_1.join)(electron_1.app.getPath('userData'), 'nexus.db');
            console.log(`Setting up database at: ${dbPath}`);
            this.notesDB = new notes_1.NotesDB(dbPath);
            // Clean up any temporary items from previous sessions
            console.log('About to clean up temporary items...');
            try {
                const beforeCount = this.notesDB.getTemporaryItemsCount();
                console.log(`Found ${beforeCount} temporary items before cleanup`);
                const cleanedCount = this.notesDB.cleanupTemporaryItems();
                console.log(`Cleanup completed. Removed ${cleanedCount} temporary items.`);
                const afterCount = this.notesDB.getTemporaryItemsCount();
                console.log(`Found ${afterCount} temporary items after cleanup`);
            }
            catch (cleanupError) {
                console.error('Error during cleanup, but continuing:', cleanupError);
            }
            console.log('Database setup completed successfully');
        }
        catch (error) {
            console.error('Failed to setup database:', error);
            // Show error dialog to user
            if (this.mainWindow) {
                this.mainWindow.webContents.executeJavaScript(`
          alert('Database initialization failed. Please restart the application. Error: ${error instanceof Error ? error.message : 'Unknown error'}');
        `).catch(() => {
                    // Ignore if webContents is not ready
                });
            }
            // Don't throw here to prevent app crash, but log the error
            // The app will continue but database operations will fail gracefully
        }
    }
    setupClipboard() {
        this.clipboardManager = new clipboard_1.ClipboardManager();
        this.clipboardManager.start();
    }
    setupIPC() {
        if (this.notesDB && this.clipboardManager && this.mainWindow) {
            (0, ipc_1.setupIpcHandlers)(this.notesDB, this.clipboardManager, this.mainWindow);
        }
    }
    getMainWindow() {
        return this.mainWindow;
    }
    getNotesDB() {
        return this.notesDB;
    }
    getClipboardManager() {
        return this.clipboardManager;
    }
    cleanup() {
        logger_1.SafeLogger.info('Starting application cleanup...');
        try {
            // Stop clipboard manager
            if (this.clipboardManager) {
                logger_1.SafeLogger.info('Stopping clipboard manager...');
                this.clipboardManager.stop();
                this.clipboardManager = null;
            }
        }
        catch (error) {
            logger_1.SafeLogger.error('Error stopping clipboard manager:', error);
        }
        try {
            // Destroy tray
            if (this.tray) {
                logger_1.SafeLogger.info('Destroying tray...');
                this.tray.destroy();
                this.tray = null;
            }
        }
        catch (error) {
            logger_1.SafeLogger.error('Error destroying tray:', error);
        }
        try {
            // Close database
            if (this.notesDB) {
                logger_1.SafeLogger.info('Closing database...');
                this.notesDB.close();
                this.notesDB = null;
            }
        }
        catch (error) {
            logger_1.SafeLogger.error('Error closing database:', error);
        }
        try {
            // Flush any pending console output
            logger_1.SafeLogger.flush();
        }
        catch (error) {
            // Ignore flush errors
        }
        logger_1.SafeLogger.info('Application cleanup completed');
    }
}
// Create the app instance only if we're running in Electron
let nexusApp = null;
// Check if we're running in Electron context
if (typeof process !== 'undefined' && process.versions && process.versions.electron) {
    nexusApp = new NexusApp();
}
exports.default = nexusApp;
