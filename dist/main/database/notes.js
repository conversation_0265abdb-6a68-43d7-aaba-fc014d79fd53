"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotesDB = void 0;
const better_sqlite3_1 = __importDefault(require("better-sqlite3"));
const fs_1 = require("fs");
const path_1 = require("path");
const uuid_1 = require("uuid");
const logger_1 = require("../utils/logger");
class NotesDB {
    constructor(dbPath) {
        try {
            // Add retry logic for database initialization
            this.db = this.initializeDatabase(dbPath);
            this.db.pragma('journal_mode = WAL');
            this.db.pragma('foreign_keys = ON');
            this.initTables();
        }
        catch (error) {
            logger_1.SafeLogger.error('Failed to initialize database:', error);
            throw new Error(`Database initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    initializeDatabase(dbPath, retries = 3) {
        for (let attempt = 1; attempt <= retries; attempt++) {
            try {
                logger_1.SafeLogger.info(`Attempting to open database (attempt ${attempt}/${retries}): ${dbPath}`);
                const db = new better_sqlite3_1.default(dbPath);
                logger_1.SafeLogger.info('Database opened successfully');
                return db;
            }
            catch (error) {
                logger_1.SafeLogger.error(`Database open attempt ${attempt} failed:`, error);
                if (attempt === retries) {
                    throw error;
                }
                // Wait before retrying (exponential backoff)
                const delay = Math.pow(2, attempt - 1) * 100; // 100ms, 200ms, 400ms
                logger_1.SafeLogger.info(`Retrying in ${delay}ms...`);
                // Synchronous delay for simplicity in constructor
                const start = Date.now();
                while (Date.now() - start < delay) {
                    // Busy wait
                }
            }
        }
        throw new Error('Failed to initialize database after all retries');
    }
    initTables() {
        try {
            // Read and execute schema
            const schemaPath = (0, path_1.join)(__dirname, 'schema.sql');
            const schema = (0, fs_1.readFileSync)(schemaPath, 'utf-8');
            // Check if we need to migrate existing database
            this.migrateDatabase();
            this.db.exec(schema);
            logger_1.SafeLogger.info('Database initialized successfully');
        }
        catch (error) {
            logger_1.SafeLogger.error('Error initializing database:', error);
            throw error;
        }
    }
    migrateDatabase() {
        try {
            // Check if parent_id column exists
            const tableInfo = this.db.prepare("PRAGMA table_info(notes)").all();
            const hasParentId = tableInfo.some(col => col.name === 'parent_id');
            const hasIsFolder = tableInfo.some(col => col.name === 'is_folder');
            const hasIsExpanded = tableInfo.some(col => col.name === 'is_expanded');
            const hasIsTemporary = tableInfo.some(col => col.name === 'is_temporary');
            if (!hasParentId || !hasIsFolder || !hasIsExpanded || !hasIsTemporary) {
                logger_1.SafeLogger.info('Migrating database to tree structure...');
                // Add new columns if they don't exist
                if (!hasParentId) {
                    this.db.exec('ALTER TABLE notes ADD COLUMN parent_id TEXT REFERENCES notes(id)');
                }
                if (!hasIsFolder) {
                    this.db.exec('ALTER TABLE notes ADD COLUMN is_folder BOOLEAN DEFAULT FALSE');
                }
                if (!hasIsExpanded) {
                    this.db.exec('ALTER TABLE notes ADD COLUMN is_expanded BOOLEAN DEFAULT TRUE');
                }
                if (!hasIsTemporary) {
                    this.db.exec('ALTER TABLE notes ADD COLUMN is_temporary BOOLEAN DEFAULT FALSE');
                }
                // Create indexes for new columns
                this.db.exec('CREATE INDEX IF NOT EXISTS idx_notes_parent_id ON notes(parent_id)');
                this.db.exec('CREATE INDEX IF NOT EXISTS idx_notes_is_folder ON notes(is_folder)');
                // Temporarily disable foreign keys for migration
                this.db.exec('PRAGMA foreign_keys = OFF');
                // Set all existing notes to have 'root' as parent if they don't have one
                this.db.exec("UPDATE notes SET parent_id = 'root' WHERE parent_id IS NULL");
                // Re-enable foreign keys
                this.db.exec('PRAGMA foreign_keys = ON');
                logger_1.SafeLogger.info('Database migration completed');
            }
        }
        catch (error) {
            logger_1.SafeLogger.error('Error during database migration:', error);
            // Don't throw here, let the main init continue
        }
    }
    // Get count of temporary items
    getTemporaryItemsCount() {
        try {
            const stmt = this.db.prepare('SELECT COUNT(*) as count FROM notes WHERE is_temporary = 1');
            const result = stmt.get();
            return result.count;
        }
        catch (error) {
            logger_1.SafeLogger.error('Error counting temporary items:', error);
            return 0;
        }
    }
    // Cleanup temporary items that were never finalized
    cleanupTemporaryItems() {
        try {
            const stmt = this.db.prepare(`
        DELETE FROM notes
        WHERE is_temporary = 1
      `);
            const result = stmt.run();
            const deletedCount = result.changes;
            if (deletedCount > 0) {
                logger_1.SafeLogger.info(`Cleaned up ${deletedCount} temporary items on startup`);
            }
            else {
                logger_1.SafeLogger.info('No temporary items found to clean up');
            }
            return deletedCount;
        }
        catch (error) {
            logger_1.SafeLogger.error('Error cleaning up temporary items:', error);
            return 0;
        }
    }
    // Note operations
    async createNote(content, title, parentId, isTemporary) {
        const id = (0, uuid_1.v4)();
        const now = new Date().toISOString();
        const stmt = this.db.prepare(`
      INSERT INTO notes (id, content, title, parent_id, is_folder, is_temporary, created_at, updated_at) 
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `);
        try {
            stmt.run(id, content, title || null, parentId || 'root', 0, isTemporary ? 1 : 0, now, now);
            return id;
        }
        catch (error) {
            logger_1.SafeLogger.error('Error creating note:', error);
            throw error;
        }
    }
    async updateNote(id, content, title) {
        const now = new Date().toISOString();
        const stmt = this.db.prepare(`
      UPDATE notes 
      SET content = ?, title = ?, updated_at = ?, is_temporary = FALSE
      WHERE id = ? AND is_deleted = FALSE
    `);
        try {
            const result = stmt.run(content, title || null, now, id);
            if (result.changes === 0) {
                throw new Error('Note not found or already deleted');
            }
        }
        catch (error) {
            logger_1.SafeLogger.error('Error updating note:', error);
            throw error;
        }
    }
    async deleteNote(id) {
        const stmt = this.db.prepare(`
      UPDATE notes 
      SET is_deleted = TRUE, updated_at = ?
      WHERE id = ?
    `);
        try {
            const result = stmt.run(new Date().toISOString(), id);
            if (result.changes === 0) {
                throw new Error('Note not found');
            }
        }
        catch (error) {
            logger_1.SafeLogger.error('Error deleting note:', error);
            throw error;
        }
    }
    async searchNotes(query) {
        if (!query.trim()) {
            const notes = await this.getAllNotes();
            return notes.map(note => ({
                id: note.id,
                title: note.title || 'Untitled',
                content: note.content,
                path: [],
                matchType: 'content'
            }));
        }
        // For simple queries (numbers, short strings), prioritize substring matching
        const isSimpleQuery = /^[a-zA-Z0-9\s]{1,10}$/.test(query.trim());
        if (isSimpleQuery) {
            // Use LIKE search for better substring matching
            logger_1.SafeLogger.info(`Using substring search for simple query: "${query}"`);
            return this.searchNotesLike(query);
        }
        // Use FTS for complex full-text search
        const stmt = this.db.prepare(`
      SELECT n.*,
             snippet(notes_fts, 1, '<mark>', '</mark>', '...', 10) as snippet
      FROM notes n
      JOIN notes_fts fts ON n.id = fts.id
      WHERE notes_fts MATCH ? AND n.is_deleted = FALSE AND n.is_folder = FALSE
      ORDER BY n.is_pinned DESC, n.updated_at DESC
      LIMIT 50
    `);
        try {
            const rows = stmt.all(query);
            const results = [];
            for (const row of rows) {
                const path = await this.getNotePath(row.id);
                results.push({
                    id: row.id,
                    title: row.title || 'Untitled',
                    content: row.content,
                    snippet: row.snippet,
                    path: path.slice(0, -1), // Remove the note itself from path
                    matchType: this.getMatchType(row, query)
                });
            }
            return results;
        }
        catch (error) {
            // Fallback to LIKE search if FTS fails
            logger_1.SafeLogger.warn('FTS search failed, falling back to LIKE search:', error);
            return this.searchNotesLike(query);
        }
    }
    async searchNotesLike(query) {
        // Simplified LIKE search to avoid SQL parameter issues
        const stmt = this.db.prepare(`
      SELECT * FROM notes
      WHERE (content LIKE ? OR title LIKE ?) AND is_deleted = FALSE AND is_folder = FALSE
      ORDER BY is_pinned DESC, updated_at DESC
      LIMIT 50
    `);
        const searchTerm = `%${query}%`;
        const rows = stmt.all(searchTerm, searchTerm);
        const results = [];
        for (const row of rows) {
            const path = await this.getNotePath(row.id);
            // Create a simple snippet for LIKE search results
            const snippet = this.createSnippet(row.content, query);
            results.push({
                id: row.id,
                title: row.title || 'Untitled',
                content: row.content,
                snippet,
                path: path.slice(0, -1),
                matchType: this.getMatchType(row, query)
            });
        }
        return results;
    }
    getMatchType(note, query) {
        const title = note.title || '';
        return title.toLowerCase().includes(query.toLowerCase()) ? 'title' : 'content';
    }
    createSnippet(content, query, maxLength = 150) {
        if (!content || !query)
            return content.substring(0, maxLength);
        const lowerContent = content.toLowerCase();
        const lowerQuery = query.toLowerCase();
        const queryIndex = lowerContent.indexOf(lowerQuery);
        if (queryIndex === -1) {
            // Query not found, return beginning of content
            return content.substring(0, maxLength) + (content.length > maxLength ? '...' : '');
        }
        // Calculate snippet boundaries
        const snippetStart = Math.max(0, queryIndex - 50);
        const snippetEnd = Math.min(content.length, queryIndex + query.length + 50);
        let snippet = content.substring(snippetStart, snippetEnd);
        // Add ellipsis if needed
        if (snippetStart > 0)
            snippet = '...' + snippet;
        if (snippetEnd < content.length)
            snippet = snippet + '...';
        // Highlight the query term (simple version)
        const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
        snippet = snippet.replace(regex, '<mark>$1</mark>');
        return snippet;
    }
    async getAllNotes() {
        const stmt = this.db.prepare(`
      SELECT * FROM notes 
      WHERE is_deleted = FALSE
      ORDER BY is_pinned DESC, updated_at DESC
      LIMIT 100
    `);
        try {
            const rows = stmt.all();
            return rows;
        }
        catch (error) {
            logger_1.SafeLogger.error('Error getting all notes:', error);
            throw error;
        }
    }
    async getNoteById(id) {
        const stmt = this.db.prepare(`
      SELECT * FROM notes 
      WHERE id = ? AND is_deleted = FALSE
    `);
        try {
            const row = stmt.get(id);
            return row;
        }
        catch (error) {
            logger_1.SafeLogger.error('Error getting note by id:', error);
            throw error;
        }
    }
    async pinNote(id, isPinned) {
        const stmt = this.db.prepare(`
      UPDATE notes 
      SET is_pinned = ?, updated_at = ?
      WHERE id = ? AND is_deleted = FALSE
    `);
        try {
            const result = stmt.run(isPinned, new Date().toISOString(), id);
            if (result.changes === 0) {
                throw new Error('Note not found');
            }
        }
        catch (error) {
            logger_1.SafeLogger.error('Error pinning note:', error);
            throw error;
        }
    }
    // Create folder as a note with is_folder = true
    async createFolder(name, parentId, isTemporary) {
        const id = (0, uuid_1.v4)();
        const now = new Date().toISOString();
        console.log('DB: createFolder called with:', { name, parentId, isTemporary, id });
        const stmt = this.db.prepare(`
      INSERT INTO notes (id, content, title, parent_id, is_folder, is_expanded, is_temporary, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
        try {
            const params = [id, '', name, parentId || 'root', 1, 1, isTemporary ? 1 : 0, now, now];
            console.log('DB: Inserting folder with params:', params);
            const result = stmt.run(...params);
            console.log('DB: Insert result:', result);
            return id;
        }
        catch (error) {
            logger_1.SafeLogger.error('Error creating folder:', error);
            throw error;
        }
    }
    // Move note to different parent
    async moveNote(noteId, newParentId) {
        const stmt = this.db.prepare(`
      UPDATE notes 
      SET parent_id = ?, updated_at = ?
      WHERE id = ? AND is_deleted = FALSE
    `);
        try {
            const result = stmt.run(newParentId || 'root', new Date().toISOString(), noteId);
            if (result.changes === 0) {
                throw new Error('Note not found or already deleted');
            }
        }
        catch (error) {
            logger_1.SafeLogger.error('Error moving note:', error);
            throw error;
        }
    }
    // Toggle folder expand/collapse state
    async toggleFolder(folderId) {
        const stmt = this.db.prepare(`
      UPDATE notes 
      SET is_expanded = NOT is_expanded, updated_at = ?
      WHERE id = ? AND is_folder = TRUE AND is_deleted = FALSE
    `);
        try {
            const result = stmt.run(new Date().toISOString(), folderId);
            if (result.changes === 0) {
                throw new Error('Folder not found or not a folder');
            }
        }
        catch (error) {
            logger_1.SafeLogger.error('Error toggling folder:', error);
            throw error;
        }
    }
    // Get notes organized as tree structure
    async getNotesTree() {
        const stmt = this.db.prepare(`
      SELECT * FROM notes
      WHERE is_deleted = FALSE
      ORDER BY is_folder DESC, is_pinned DESC, title ASC, updated_at DESC
    `);
        try {
            const rows = stmt.all();
            console.log('DB: getNotesTree found', rows.length, 'rows');
            console.log('DB: Raw rows:', rows);
            const tree = this.buildTree(rows);
            console.log('DB: Built tree with', tree.length, 'root nodes');
            console.log('DB: Tree structure:', tree);
            return tree;
        }
        catch (error) {
            logger_1.SafeLogger.error('Error getting notes tree:', error);
            throw error;
        }
    }
    // Build hierarchical tree from flat notes array
    buildTree(notes) {
        const nodeMap = new Map();
        const roots = [];
        // First pass: create all nodes
        notes.forEach(note => {
            const node = {
                id: note.id,
                title: note.title || (note.is_folder ? 'Untitled Folder' : 'Untitled Note'),
                content: note.content,
                parentId: note.parent_id || null,
                children: [],
                isFolder: note.is_folder || false,
                isExpanded: note.is_expanded || true,
                isPinned: note.is_pinned || false,
                isTemporary: note.is_temporary || false,
                createdAt: new Date(note.created_at),
                updatedAt: new Date(note.updated_at)
            };
            nodeMap.set(note.id, node);
        });
        // Second pass: build parent-child relationships
        nodeMap.forEach(node => {
            if (node.parentId && nodeMap.has(node.parentId) && node.parentId !== node.id) {
                // Add node as child of its parent (but not if it's its own parent)
                nodeMap.get(node.parentId).children.push(node);
            }
            else {
                // Node has no parent or is its own parent - treat as root
                roots.push(node);
            }
        });
        console.log('DB: buildTree processed', nodeMap.size, 'nodes, found', roots.length, 'roots');
        return roots;
    }
    // Get breadcrumb path for a note
    async getNotePath(noteId) {
        const path = [];
        let currentId = noteId;
        while (currentId) {
            const stmt = this.db.prepare(`
        SELECT id, title, parent_id, is_folder FROM notes 
        WHERE id = ? AND is_deleted = FALSE
      `);
            const note = stmt.get(currentId);
            if (!note)
                break;
            path.unshift(note.title || (note.is_folder ? 'Untitled Folder' : 'Untitled Note'));
            currentId = note.parent_id || null;
        }
        return path;
    }
    async getAllFolders() {
        const stmt = this.db.prepare(`
      SELECT * FROM folders 
      WHERE is_deleted = FALSE
      ORDER BY name ASC
    `);
        try {
            const rows = stmt.all();
            return rows;
        }
        catch (error) {
            logger_1.SafeLogger.error('Error getting all folders:', error);
            throw error;
        }
    }
    // Cleanup and maintenance
    async cleanup() {
        // Remove old deleted notes (older than 30 days)
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const stmt = this.db.prepare(`
      DELETE FROM notes 
      WHERE is_deleted = TRUE AND updated_at < ?
    `);
        try {
            const result = stmt.run(thirtyDaysAgo.toISOString());
            logger_1.SafeLogger.info(`Cleaned up ${result.changes} old deleted notes`);
        }
        catch (error) {
            logger_1.SafeLogger.error('Error during cleanup:', error);
        }
    }
    close() {
        this.db.close();
    }
}
exports.NotesDB = NotesDB;
