-- Cleanup script for temporary items that were never finalized
-- This will help us start fresh and debug the inline editing issue

-- First, let's see what temporary items exist
SELECT 
  id, 
  title, 
  is_folder, 
  is_temporary, 
  created_at,
  updated_at
FROM notes 
WHERE is_temporary = 1 
ORDER BY created_at DESC;

-- Delete all temporary items (they were never properly finalized)
-- This is safe because temporary items should only exist during active editing
DELETE FROM notes WHERE is_temporary = 1;

-- Verify cleanup
SELECT COUNT(*) as remaining_temporary_items FROM notes WHERE is_temporary = 1;

-- Show remaining notes
SELECT 
  id, 
  title, 
  is_folder, 
  is_temporary, 
  created_at
FROM notes 
WHERE is_deleted = 0 
ORDER BY created_at DESC 
LIMIT 10;
