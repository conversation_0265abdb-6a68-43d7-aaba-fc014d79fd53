/**
 * Enhanced Inline Create & Rename Implementation
 * 
 * This file contains improved versions of the functions described in inline_create_rename_prompt.md
 * with comprehensive error handling, validation, accessibility, and performance optimizations.
 */

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { NoteNode } from '../shared/types';

// ============================================================================
// TYPES AND INTERFACES
// ============================================================================

interface ValidationResult {
  isValid: boolean;
  error?: string;
  warnings?: string[];
}

interface EditState {
  value: string;
  isValidating: boolean;
  validationError: string | null;
  hasUnsavedChanges: boolean;
}

interface EditSession {
  nodeId: string;
  originalName: string;
  startTime: number;
  isTemporary: boolean;
}

interface OptimisticUpdate {
  id: string;
  type: 'create' | 'update' | 'delete';
  data: any;
  timestamp: number;
  rollback: () => Promise<void>;
}

interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  message: string;
  duration?: number;
  actions?: Array<{
    label: string;
    action: () => void;
  }>;
}

interface ErrorContext {
  operation: string;
  nodeId?: string;
  nodeName?: string;
  parentId?: string;
}

// ============================================================================
// ENHANCED VALIDATION FUNCTIONS
// ============================================================================

/**
 * Enhanced name validation with platform-specific rules and duplicate checking
 */
export const validateNodeName = async (
  name: string, 
  parentId: string | null, 
  excludeId?: string
): Promise<ValidationResult> => {
  const trimmedName = name.trim();
  
  // Basic validation
  if (!trimmedName) {
    return { isValid: false, error: 'Name cannot be empty' };
  }
  
  if (trimmedName.length > 255) {
    return { isValid: false, error: 'Name too long (max 255 characters)' };
  }
  
  if (trimmedName.length < 1) {
    return { isValid: false, error: 'Name too short' };
  }
  
  // Platform-specific invalid characters
  const platform = navigator.platform.toLowerCase();
  let invalidChars: RegExp;
  
  if (platform.includes('win')) {
    invalidChars = /[<>:"/\\|?*\x00-\x1f]/;
  } else if (platform.includes('mac')) {
    invalidChars = /[:\x00-\x1f]/;
  } else {
    invalidChars = /[/\x00]/;
  }
  
  if (invalidChars.test(trimmedName)) {
    return { 
      isValid: false, 
      error: `Name contains invalid characters for ${platform.includes('win') ? 'Windows' : platform.includes('mac') ? 'macOS' : 'this system'}` 
    };
  }
  
  // Reserved names check (Windows)
  const reservedNames = [
    'CON', 'PRN', 'AUX', 'NUL',
    'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9',
    'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
  ];
  
  if (platform.includes('win') && reservedNames.includes(trimmedName.toUpperCase())) {
    return { isValid: false, error: 'Name is reserved by the system' };
  }
  
  // Check for duplicate names in the same parent
  try {
    const siblings = await getSiblingNodes(parentId);
    const duplicateExists = siblings.some(sibling => 
      sibling.id !== excludeId && 
      (sibling.title || '').toLowerCase() === trimmedName.toLowerCase()
    );
    
    if (duplicateExists) {
      return { isValid: false, error: 'A file or folder with this name already exists' };
    }
  } catch (error) {
    console.warn('Could not check for duplicates:', error);
  }
  
  // Warnings for potentially problematic names
  const warnings: string[] = [];
  
  if (trimmedName.startsWith('.')) {
    warnings.push('Names starting with a dot are hidden on Unix systems');
  }
  
  if (trimmedName.includes('  ')) {
    warnings.push('Multiple consecutive spaces may cause display issues');
  }
  
  if (/^\d+$/.test(trimmedName)) {
    warnings.push('Numeric-only names may be confusing');
  }
  
  return { 
    isValid: true, 
    warnings: warnings.length > 0 ? warnings : undefined 
  };
};

/**
 * Enhanced unique name generation with better conflict resolution
 */
export const generateUniqueName = async (
  baseName: string, 
  parentId: string | null,
  maxAttempts: number = 100
): Promise<string> => {
  try {
    const siblings = await getSiblingNodes(parentId);
    const existingNames = new Set(
      siblings.map(s => (s.title || '').toLowerCase())
    );
    
    // If base name is available, use it
    if (!existingNames.has(baseName.toLowerCase())) {
      return baseName;
    }
    
    // Try numbered variations
    for (let i = 1; i <= maxAttempts; i++) {
      const candidate = `${baseName} (${i})`;
      if (!existingNames.has(candidate.toLowerCase())) {
        return candidate;
      }
    }
    
    // Fallback to timestamp-based name
    const timestamp = Date.now();
    return `${baseName} (${timestamp})`;
    
  } catch (error) {
    console.error('Error generating unique name:', error);
    const timestamp = Date.now();
    return `${baseName} (${timestamp})`;
  }
};

// ============================================================================
// HELPER FUNCTIONS (to be implemented based on your data layer)
// ============================================================================

/**
 * Get sibling nodes for duplicate checking
 */
const getSiblingNodes = async (parentId: string | null): Promise<NoteNode[]> => {
  // This should be implemented based on your data access layer
  // For now, returning empty array as placeholder
  return [];
};

/**
 * Validate that parent exists and is a folder
 */
const validateParentExists = async (parentId: string): Promise<boolean> => {
  // Implementation depends on your data layer
  return true;
};

/**
 * Get node by ID
 */
const getNodeById = async (nodeId: string): Promise<NoteNode | null> => {
  // Implementation depends on your data layer
  return null;
};

/**
 * Get notes tree
 */
const getNotesTree = async (): Promise<NoteNode[]> => {
  // Implementation depends on your data layer
  return [];
};

// ============================================================================
// PLACEHOLDER FUNCTIONS (to be connected to your actual implementations)
// ============================================================================

const createNote = async (data: any): Promise<string> => {
  // Connect to your actual createNote implementation
  throw new Error('Not implemented');
};

const createFolder = async (data: any): Promise<string> => {
  // Connect to your actual createFolder implementation
  throw new Error('Not implemented');
};

const deleteNote = async (id: string): Promise<void> => {
  // Connect to your actual deleteNote implementation
  throw new Error('Not implemented');
};

const deleteFolder = async (id: string): Promise<void> => {
  // Connect to your actual deleteFolder implementation
  throw new Error('Not implemented');
};

const addOptimisticNoteToTree = (note: NoteNode): void => {
  // Connect to your tree state management
};

const addOptimisticFolderToTree = (folder: NoteNode): void => {
  // Connect to your tree state management
};

const removeOptimisticNote = (id: string): void => {
  // Connect to your tree state management
};

const removeOptimisticFolder = (id: string): void => {
  // Connect to your tree state management
};

const updateOptimisticNote = async (tempId: string, realId: string): Promise<void> => {
  // Connect to your tree state management
};

const updateOptimisticFolder = async (tempId: string, realId: string): Promise<void> => {
  // Connect to your tree state management
};

const enterInlineEditMode = async (nodeId: string, options: any): Promise<void> => {
  // Connect to your edit state management
};

const showErrorNotification = (message: string): void => {
  // Connect to your notification system
  console.error(message);
};

// Current folder ID - should be managed by your app state
let currentFolderId: string | null = null;

// ============================================================================
// ENHANCED CREATION FUNCTIONS
// ============================================================================

/**
 * Enhanced note creation with proper error handling and state management
 */
export const createNewNote = async (parentFolderId?: string): Promise<string | null> => {
  // Validate parent folder exists if specified
  if (parentFolderId && !await validateParentExists(parentFolderId)) {
    throw new Error(`Parent folder ${parentFolderId} does not exist`);
  }

  // Generate unique name to prevent conflicts
  const baseName = "Untitled";
  const uniqueName = await generateUniqueName(baseName, parentFolderId);

  let noteId: string | null = null;
  let rollbackRequired = false;
  let optimisticNote: NoteNode | null = null;

  try {
    // 1. Create optimistic note for immediate UI feedback
    optimisticNote = {
      id: `temp-${Date.now()}`, // Temporary ID for immediate UI feedback
      title: uniqueName,
      content: "",
      parentId: parentFolderId || null,
      children: [],
      isFolder: false,
      isExpanded: false,
      isPinned: false,
      isTemporary: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // 2. Add to UI immediately for instant feedback
    addOptimisticNoteToTree(optimisticNote);

    // 3. Create in database
    noteId = await createNote({
      title: uniqueName,
      content: "",
      parentId: parentFolderId || currentFolderId,
      isTemporary: true
    });

    rollbackRequired = true;

    // 4. Update optimistic note with real ID
    await updateOptimisticNote(optimisticNote.id, noteId);

    // 5. Enter edit mode with proper focus management
    await enterInlineEditMode(noteId, {
      selectAll: true,
      scrollIntoView: true,
      preventBlur: true
    });

    return noteId;

  } catch (error) {
    // Rollback optimistic update
    if (optimisticNote) {
      removeOptimisticNote(optimisticNote.id);
    }

    // Cleanup database if note was created
    if (rollbackRequired && noteId) {
      try {
        await deleteNote(noteId);
      } catch (cleanupError) {
        console.error('Failed to cleanup note after error:', cleanupError);
      }
    }

    // Show user-friendly error
    showErrorNotification('Failed to create note. Please try again.');
    throw error;
  }
};

/**
 * Enhanced folder creation with proper error handling and state management
 */
export const createNewFolder = async (parentFolderId?: string): Promise<string | null> => {
  // Validate parent folder exists and is actually a folder
  if (parentFolderId) {
    const parentNode = await getNodeById(parentFolderId);
    if (!parentNode || !parentNode.isFolder) {
      throw new Error(`Invalid parent folder: ${parentFolderId}`);
    }
  }

  // Generate unique folder name
  const baseName = "New Folder";
  const uniqueName = await generateUniqueName(baseName, parentFolderId);

  let folderId: string | null = null;
  let rollbackRequired = false;
  let optimisticFolder: NoteNode | null = null;

  try {
    // 1. Create optimistic folder for immediate UI feedback
    optimisticFolder = {
      id: `temp-folder-${Date.now()}`,
      title: uniqueName,
      content: "",
      parentId: parentFolderId || null,
      children: [],
      isFolder: true,
      isExpanded: true, // Auto-expand new folders
      isPinned: false,
      isTemporary: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // 2. Add to UI immediately
    addOptimisticFolderToTree(optimisticFolder);

    // 3. Create in database
    folderId = await createFolder({
      name: uniqueName,
      parentId: parentFolderId || currentFolderId,
      isTemporary: true
    });

    rollbackRequired = true;

    // 4. Update optimistic folder with real ID
    await updateOptimisticFolder(optimisticFolder.id, folderId);

    // 5. Enter edit mode
    await enterInlineEditMode(folderId, {
      selectAll: true,
      scrollIntoView: true,
      preventBlur: true
    });

    return folderId;

  } catch (error) {
    // Rollback optimistic update
    if (optimisticFolder) {
      removeOptimisticFolder(optimisticFolder.id);
    }

    // Cleanup database if folder was created
    if (rollbackRequired && folderId) {
      try {
        await deleteFolder(folderId);
      } catch (cleanupError) {
        console.error('Failed to cleanup folder after error:', cleanupError);
      }
    }

    showErrorNotification('Failed to create folder. Please try again.');
    throw error;
  }
};

// ============================================================================
// ENHANCED HOOKS FOR STATE MANAGEMENT
// ============================================================================

/**
 * Enhanced edit lock with session management and optimistic updates
 */
export const useEditLock = () => {
  const [editingSessions, setEditingSessions] = useState<Map<string, EditSession>>(new Map());
  const [optimisticUpdates, setOptimisticUpdates] = useState<Map<string, OptimisticUpdate>>(new Map());

  const startEdit = useCallback((nodeId: string, originalName: string, isTemporary: boolean = false): boolean => {
    setEditingSessions(prev => {
      if (prev.has(nodeId)) {
        console.warn(`Node ${nodeId} is already being edited`);
        return prev;
      }

      const newSessions = new Map(prev);
      newSessions.set(nodeId, {
        nodeId,
        originalName,
        startTime: Date.now(),
        isTemporary
      });
      return newSessions;
    });

    return true;
  }, []);

  const endEdit = useCallback((nodeId: string) => {
    setEditingSessions(prev => {
      const newSessions = new Map(prev);
      newSessions.delete(nodeId);
      return newSessions;
    });
  }, []);

  const getEditSession = useCallback((nodeId: string): EditSession | undefined => {
    return editingSessions.get(nodeId);
  }, [editingSessions]);

  const isEditing = useCallback((nodeId: string): boolean => {
    return editingSessions.has(nodeId);
  }, [editingSessions]);

  // Cleanup stale edit sessions (older than 5 minutes)
  const cleanupStaleSessions = useCallback(() => {
    const now = Date.now();
    const staleThreshold = 5 * 60 * 1000; // 5 minutes

    setEditingSessions(prev => {
      const newSessions = new Map(prev);
      for (const [nodeId, session] of prev) {
        if (now - session.startTime > staleThreshold) {
          console.warn(`Cleaning up stale edit session for node ${nodeId}`);
          newSessions.delete(nodeId);
        }
      }
      return newSessions;
    });
  }, []);

  // Auto-cleanup every minute
  useEffect(() => {
    const interval = setInterval(cleanupStaleSessions, 60000);
    return () => clearInterval(interval);
  }, [cleanupStaleSessions]);

  // Optimistic update management
  const addOptimisticUpdate = useCallback((update: OptimisticUpdate) => {
    setOptimisticUpdates(prev => new Map(prev).set(update.id, update));
  }, []);

  const removeOptimisticUpdate = useCallback((id: string) => {
    setOptimisticUpdates(prev => {
      const newUpdates = new Map(prev);
      newUpdates.delete(id);
      return newUpdates;
    });
  }, []);

  const rollbackOptimisticUpdate = useCallback(async (id: string) => {
    const update = optimisticUpdates.get(id);
    if (update) {
      try {
        await update.rollback();
        removeOptimisticUpdate(id);
      } catch (error) {
        console.error(`Failed to rollback optimistic update ${id}:`, error);
      }
    }
  }, [optimisticUpdates, removeOptimisticUpdate]);

  return {
    startEdit,
    endEdit,
    getEditSession,
    isEditing,
    cleanupStaleSessions,
    addOptimisticUpdate,
    removeOptimisticUpdate,
    rollbackOptimisticUpdate,
    editingSessions: Array.from(editingSessions.values()),
    optimisticUpdates: Array.from(optimisticUpdates.values())
  };
};

/**
 * Enhanced notification system for user feedback
 */
export const useNotifications = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  const showNotification = useCallback((notification: Omit<Notification, 'id'>) => {
    const id = `notification-${Date.now()}-${Math.random()}`;
    const newNotification: Notification = {
      ...notification,
      id,
      duration: notification.duration ?? 5000
    };

    setNotifications(prev => [...prev, newNotification]);

    // Auto-remove after duration
    if (newNotification.duration > 0) {
      setTimeout(() => {
        removeNotification(id);
      }, newNotification.duration);
    }

    return id;
  }, []);

  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  }, []);

  const showErrorNotification = useCallback((message: string, actions?: Notification['actions']) => {
    return showNotification({
      type: 'error',
      message,
      actions,
      duration: 8000 // Longer duration for errors
    });
  }, [showNotification]);

  const showSuccessNotification = useCallback((message: string) => {
    return showNotification({
      type: 'success',
      message,
      duration: 3000
    });
  }, [showNotification]);

  return {
    notifications,
    showNotification,
    removeNotification,
    showErrorNotification,
    showSuccessNotification
  };
};

// ============================================================================
// ERROR HANDLING UTILITIES
// ============================================================================

/**
 * Enhanced error handling with user-friendly messages
 */
export const handleTreeError = (error: Error, context: ErrorContext): string => {
  console.error(`Tree operation failed:`, error, context);

  // Network errors
  if (error.message.includes('fetch') || error.message.includes('network')) {
    return 'Network error. Please check your connection and try again.';
  }

  // Permission errors
  if (error.message.includes('permission') || error.message.includes('unauthorized')) {
    return 'Permission denied. You may not have access to perform this action.';
  }

  // Validation errors
  if (error.message.includes('validation') || error.message.includes('invalid')) {
    return error.message; // These are usually user-friendly already
  }

  // Database errors
  if (error.message.includes('database') || error.message.includes('sql')) {
    return 'Database error. Please try again or contact support.';
  }

  // Specific operation errors
  switch (context.operation) {
    case 'create_note':
      return 'Failed to create note. Please try again.';
    case 'create_folder':
      return 'Failed to create folder. Please try again.';
    case 'rename':
      return `Failed to rename "${context.nodeName}". Please try again.`;
    case 'delete':
      return `Failed to delete "${context.nodeName}". Please try again.`;
    default:
      return 'An unexpected error occurred. Please try again.';
  }
};

/**
 * Retry mechanism for failed operations
 */
export const withRetry = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: Error;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;

      if (attempt === maxRetries) {
        throw lastError;
      }

      // Exponential backoff
      const waitTime = delay * Math.pow(2, attempt - 1);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }

  throw lastError!;
};

/**
 * Debounced function utility
 */
export const useDebounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): T => {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return useCallback((...args: Parameters<T>) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      func(...args);
    }, delay);
  }, [func, delay]) as T;
};

// ============================================================================
// KEYBOARD SHORTCUTS AND ACCESSIBILITY
// ============================================================================

/**
 * Enhanced keyboard event handler for tree navigation
 */
export const useTreeKeyboardNavigation = (
  onCreateNote: () => void,
  onCreateFolder: () => void,
  onStartRename: () => void,
  onDelete: () => void
) => {
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
    const cmdOrCtrl = isMac ? e.metaKey : e.ctrlKey;

    // Prevent default for handled keys
    const handledKeys = ['Enter', 'F2', 'Delete', 'Backspace'];
    if (handledKeys.includes(e.key) || (cmdOrCtrl && ['n', 'N'].includes(e.key))) {
      e.preventDefault();
    }

    // Handle shortcuts
    if (cmdOrCtrl && e.key.toLowerCase() === 'n') {
      if (e.shiftKey) {
        onCreateFolder();
      } else {
        onCreateNote();
      }
    } else if (e.key === 'F2' || e.key === 'Enter') {
      onStartRename();
    } else if (e.key === 'Delete' || (isMac && e.key === 'Backspace')) {
      onDelete();
    }
  }, [onCreateNote, onCreateFolder, onStartRename, onDelete]);

  return { handleKeyDown };
};
