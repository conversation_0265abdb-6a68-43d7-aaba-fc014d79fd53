import { app, BrowserWindow } from 'electron';
import { join } from 'path';
import { SimpleTray } from './tray';
import { registerShortcuts } from './shortcuts';
import { setupIpcHandlers } from './ipc';
import { NotesDB } from '../database/notes';
import { ClipboardManager } from './clipboard';
import { SafeLogger } from '../utils/logger';

class NexusApp {
  private mainWindow: BrowserWindow | null = null;
  private tray: SimpleTray | null = null;
  private notesDB: NotesDB | null = null;
  private clipboardManager: ClipboardManager | null = null;

  constructor() {
    this.setupApp();
  }

  private setupApp() {
    console.log('setupApp called');

    // Ensure single instance
    const gotTheLock = app.requestSingleInstanceLock();
    console.log('Got lock:', gotTheLock);

    if (!gotTheLock) {
      console.log('Another instance is running, quitting');
      app.quit();
      return;
    }

    // Handle second instance attempt
    app.on('second-instance', () => {
      // Someone tried to run a second instance, focus our window instead
      if (this.mainWindow) {
        if (this.mainWindow.isMinimized()) {
          this.mainWindow.restore();
        }
        this.mainWindow.show();
        this.mainWindow.focus();
      }
    });

    // Handle app ready
    app.whenReady().then(() => {
      console.log('App ready event fired');
      try {
        console.log('About to create window');
        this.createWindow();
        console.log('Window created successfully');
        this.setupTray();
        this.setupShortcuts();
        this.setupDatabase();
        this.setupClipboard();
        this.setupIPC();
        console.log('App initialization completed');
      } catch (error) {
        console.error('Error during app initialization:', error);
      }
    });

    // Handle window closed
    app.on('window-all-closed', () => {
      // On macOS, keep app running even when all windows are closed
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });

    // Handle app activation (macOS)
    app.on('activate', () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        this.createWindow();
      }
    });

    // Handle before quit
    app.on('before-quit', () => {
      SafeLogger.info('App is quitting...');
      this.cleanup();
    });

    // Handle process termination signals gracefully
    process.on('SIGINT', () => {
      SafeLogger.info('Received SIGINT, shutting down gracefully...');
      app.quit();
    });

    process.on('SIGTERM', () => {
      SafeLogger.info('Received SIGTERM, shutting down gracefully...');
      app.quit();
    });

    // Handle uncaught exceptions to prevent crashes
    process.on('uncaughtException', (error) => {
      SafeLogger.error('Uncaught exception:', error);

      // Check if this is an EIO error and handle gracefully
      if (error.message && error.message.includes('EIO')) {
        SafeLogger.error('EIO error detected - likely due to process termination or file system issues');
        this.cleanup();
        // Don't exit immediately for EIO errors, they're often recoverable
        return;
      }

      // For other critical errors, show dialog and exit gracefully
      if (this.mainWindow && !this.mainWindow.isDestroyed()) {
        this.mainWindow.webContents.executeJavaScript(`
          alert('A critical error occurred: ${error.message}. The application will restart.');
        `).catch(() => {
          // Ignore if webContents is not ready
        }).finally(() => {
          app.relaunch();
          app.exit(1);
        });
      } else {
        app.relaunch();
        app.exit(1);
      }
    });

    process.on('unhandledRejection', (reason, promise) => {
      SafeLogger.error('Unhandled rejection at:', promise, 'reason:', reason);

      // Check if this is an EIO-related rejection
      const reasonStr = String(reason);
      if (reasonStr.includes('EIO') || reasonStr.includes('write')) {
        SafeLogger.error('EIO-related promise rejection detected');
        this.cleanup();
        return;
      }

      // Don't exit immediately for promise rejections, just log them
    });

  }

  private createWindow() {
    // Prevent creating multiple windows
    if (this.mainWindow) {
      this.mainWindow.show();
      this.mainWindow.focus();
      return;
    }

    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      minWidth: 600,
      minHeight: 400,
      titleBarStyle: 'hiddenInset',
      trafficLightPosition: { x: -100, y: -100 }, // Hide traffic lights
      backgroundColor: '#ffffff', // White background
      show: false, // Don't show until ready
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: join(__dirname, '../preload/preload.js'),
        webSecurity: false, // Disable for localhost in development
      },
    });

    // Load the React app
    if (process.env.NODE_ENV === 'development') {
      SafeLogger.info('Loading development URL: http://localhost:5173');
      this.mainWindow.loadURL('http://localhost:5173');
      // Auto-open DevTools for debugging
      this.mainWindow.webContents.openDevTools();
      // Force show window immediately for debugging
      this.mainWindow.show();
      this.mainWindow.focus();
      this.mainWindow.moveTop();
    } else {
      this.mainWindow.loadFile(join(__dirname, '../../renderer/index.html'));
    }

    // Handle window events
    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });

    // Hide window instead of closing on macOS
    this.mainWindow.on('close', (event) => {
      if (process.platform === 'darwin') {
        event.preventDefault();
        this.mainWindow?.hide();
      }
    });

    // Handle content loading
    this.mainWindow.webContents.on('did-finish-load', () => {
      SafeLogger.info('Renderer content loaded');
      // Show and focus the window after content loads
      this.mainWindow?.show();
      this.mainWindow?.focus();
      this.mainWindow?.moveTop();
    });

    this.mainWindow.webContents.on('did-fail-load', (_event, errorCode, errorDescription, validatedURL) => {
      SafeLogger.error('Failed to load renderer:', errorCode, errorDescription, validatedURL);
    });

    // Window is already shown, just log when it's ready
    this.mainWindow.once('ready-to-show', () => {
      SafeLogger.info('Window ready to show');
    });
  }

  private setupTray() {
    if (this.mainWindow) {
      this.tray = new SimpleTray(this.mainWindow);
    }
  }

  private setupShortcuts() {
    if (this.mainWindow) {
      registerShortcuts(this.mainWindow);
    }
  }

  private setupDatabase() {
    try {
      const dbPath = join(app.getPath('userData'), 'nexus.db');
      console.log(`Setting up database at: ${dbPath}`);
      this.notesDB = new NotesDB(dbPath);

      // Clean up any temporary items from previous sessions
      console.log('About to clean up temporary items...');
      try {
        const beforeCount = this.notesDB.getTemporaryItemsCount();
        console.log(`Found ${beforeCount} temporary items before cleanup`);

        const cleanedCount = this.notesDB.cleanupTemporaryItems();
        console.log(`Cleanup completed. Removed ${cleanedCount} temporary items.`);

        const afterCount = this.notesDB.getTemporaryItemsCount();
        console.log(`Found ${afterCount} temporary items after cleanup`);
      } catch (cleanupError) {
        console.error('Error during cleanup, but continuing:', cleanupError);
      }

      console.log('Database setup completed successfully');
    } catch (error) {
      console.error('Failed to setup database:', error);

      // Show error dialog to user
      if (this.mainWindow) {
        this.mainWindow.webContents.executeJavaScript(`
          alert('Database initialization failed. Please restart the application. Error: ${error instanceof Error ? error.message : 'Unknown error'}');
        `).catch(() => {
          // Ignore if webContents is not ready
        });
      }

      // Don't throw here to prevent app crash, but log the error
      // The app will continue but database operations will fail gracefully
    }
  }

  private setupClipboard() {
    this.clipboardManager = new ClipboardManager();
    this.clipboardManager.start();
  }

  private setupIPC() {
    if (this.notesDB && this.clipboardManager && this.mainWindow) {
      setupIpcHandlers(this.notesDB, this.clipboardManager, this.mainWindow);
    }
  }

  public getMainWindow(): BrowserWindow | null {
    return this.mainWindow;
  }

  public getNotesDB(): NotesDB | null {
    return this.notesDB;
  }

  public getClipboardManager(): ClipboardManager | null {
    return this.clipboardManager;
  }

  private cleanup(): void {
    SafeLogger.info('Starting application cleanup...');

    try {
      // Stop clipboard manager
      if (this.clipboardManager) {
        SafeLogger.info('Stopping clipboard manager...');
        this.clipboardManager.stop();
        this.clipboardManager = null;
      }
    } catch (error) {
      SafeLogger.error('Error stopping clipboard manager:', error);
    }

    try {
      // Destroy tray
      if (this.tray) {
        SafeLogger.info('Destroying tray...');
        this.tray.destroy();
        this.tray = null;
      }
    } catch (error) {
      SafeLogger.error('Error destroying tray:', error);
    }

    try {
      // Close database
      if (this.notesDB) {
        SafeLogger.info('Closing database...');
        this.notesDB.close();
        this.notesDB = null;
      }
    } catch (error) {
      SafeLogger.error('Error closing database:', error);
    }

    try {
      // Flush any pending console output
      SafeLogger.flush();
    } catch (error) {
      // Ignore flush errors
    }

    SafeLogger.info('Application cleanup completed');
  }
}

// Create the app instance only if we're running in Electron
let nexusApp: NexusApp | null = null;

// Check if we're running in Electron context
if (typeof process !== 'undefined' && process.versions && process.versions.electron) {
  nexusApp = new NexusApp();
}

export default nexusApp;
